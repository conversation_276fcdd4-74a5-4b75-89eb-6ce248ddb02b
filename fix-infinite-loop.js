// This script contains the fixes for the infinite loop issue
// To implement these changes, copy the relevant sections to the main file

// 1. Add a debounce function to prevent rapid-fire calculations
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}

// 2. Modify the event listeners to use a preventLoop flag
// For main accommodation duration dropdown
accommodationDurationSelect.addEventListener('change', function(event) {
    console.log('Accommodation duration changed to: ' + this.value);
    
    // Check if this event was triggered with the preventLoop flag
    const preventLoop = event.detail && event.detail.preventLoop;
    
    updateChristmasSectionVisibility(); // Check overlap with new duration

    // Update additional accommodation options when main accommodation duration changes
    // Only if this wasn't triggered by an internal update (to prevent loops)
    if (!preventLoop && additionalAccommodationSection.style.display !== 'none' && additionalAccommodationSelect.value) {
        console.log('Main accommodation duration changed, updating additional accommodation options');
        populateAdditionalAccommodationDurationDropdown();
    }

    // Trigger calculation if needed
    if (startDateInput.value && courseSelect.value && courseDurationSelect.value) {
         autoCalculate();
    }
});

// For additional accommodation duration dropdown
additionalAccommodationDurationSelect.addEventListener('change', function(event) {
    console.log('Additional accommodation duration changed to: ' + this.value);
    
    // Check if this event was triggered with the preventLoop flag
    const preventLoop = event.detail && event.detail.preventLoop;

    // Update main accommodation options when additional accommodation changes
    // Only if this wasn't triggered by an internal update (to prevent loops)
    if (!preventLoop && accommodationSelect.value) {
        console.log('Additional accommodation duration changed, updating main accommodation options');
        populateAccommodationWeeks();
    }

    // Trigger calculation if needed
    if (startDateInput.value && additionalAccommodationSelect.value && this.value) {
        autoCalculate();
    }
});

// 3. Modify the populateAccommodationWeeks function to use the preventLoop flag
// In the populateAccommodationWeeks function, replace:
// accommodationDurationSelect.dispatchEvent(new Event('change'));
// with:
if (valueSelected) {
    // Use a flag to prevent infinite loops
    const preventLoop = true;
    // Create a custom event with data
    const customEvent = new CustomEvent('change', { detail: { preventLoop } });
    accommodationDurationSelect.dispatchEvent(customEvent);
}

// 4. Modify the populateAdditionalAccommodationDurationDropdown function to use the preventLoop flag
// In the populateAdditionalAccommodationDurationDropdown function, replace:
// additionalAccommodationDurationSelect.dispatchEvent(new Event('change'));
// with:
if (valueSelected) {
    // Use a flag to prevent infinite loops
    const preventLoop = true;
    // Create a custom event with data
    const customEvent = new CustomEvent('change', { detail: { preventLoop } });
    additionalAccommodationDurationSelect.dispatchEvent(customEvent);
}

// 5. Create a debounced version of the autoCalculate function
const autoCalculate = debounce(_autoCalculate, 300); // 300ms debounce
